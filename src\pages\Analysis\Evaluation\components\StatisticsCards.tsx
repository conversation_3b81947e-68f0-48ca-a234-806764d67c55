import {
  StarFilled,
  TrophyOutlined,
  UserOutlined,
  SmileOutlined,
  CalendarOutlined,
  RiseOutlined,
} from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { Card, Col, Row, Statistic } from 'antd';
import React from 'react';

interface StatisticsData {
  totalReviews: number;
  todayReviews: number;
  averageRating: number;
  positiveRate: number;
  thisWeekReviews: number;
  thisMonthReviews: number;
}

interface StatisticsCardsProps {
  statistics: StatisticsData;
  loading: boolean;
}

const StatisticsCards: React.FC<StatisticsCardsProps> = ({
  statistics,
  loading,
}) => {
  return (
    <ProCard
      title="评价数据概览"
      style={{ marginBottom: 16 }}
      loading={loading}
    >
      <Row gutter={[16, 16]}>
        <Col xs={12} sm={8} md={6} lg={4} xl={4}>
          <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
            <Statistic
              title="总评价数"
              value={statistics.totalReviews}
              prefix={<UserOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff', fontSize: '18px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4} xl={4}>
          <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
            <Statistic
              title="今日评价"
              value={statistics.todayReviews}
              prefix={<CalendarOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a', fontSize: '18px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4} xl={4}>
          <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
            <Statistic
              title="平均评分"
              value={statistics.averageRating}
              prefix={<StarFilled style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14', fontSize: '18px' }}
              precision={1}
              suffix="分"
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4} xl={4}>
          <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
            <Statistic
              title="好评率"
              value={statistics.positiveRate}
              prefix={<SmileOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ color: '#722ed1', fontSize: '18px' }}
              precision={1}
              suffix="%"
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4} xl={4}>
          <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
            <Statistic
              title="本周评价"
              value={statistics.thisWeekReviews}
              prefix={<RiseOutlined style={{ color: '#eb2f96' }} />}
              valueStyle={{ color: '#eb2f96', fontSize: '18px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4} xl={4}>
          <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
            <Statistic
              title="本月评价"
              value={statistics.thisMonthReviews}
              prefix={<TrophyOutlined style={{ color: '#13c2c2' }} />}
              valueStyle={{ color: '#13c2c2', fontSize: '18px' }}
            />
          </Card>
        </Col>
      </Row>
    </ProCard>
  );
};

export default StatisticsCards;
