# 评价数据分析接口文档

## 概述
本文档描述了评价数据分析界面所需的后台API接口及其返回数据结构要求。

## 接口列表

### 1. 评价列表查询接口
**接口地址：** `GET /reviews`

**请求参数：**
```typescript
{
  current?: number;        // 当前页码，默认1
  pageSize?: number;       // 每页条数，默认20
  keyword?: string;        // 搜索关键词
  rating?: number;         // 评分筛选（1-5）
  startDate?: string;      // 开始日期 YYYY-MM-DD
  endDate?: string;        // 结束日期 YYYY-MM-DD
  customerId?: number;     // 客户ID筛选
  employeeId?: number;     // 员工ID筛选
}
```

**返回数据结构：**
```typescript
{
  errCode: number;
  msg?: string;
  data?: {
    total: number;
    list: Array<{
      id: number;                    // 评价ID
      orderId: number;               // 订单ID
      rating: number;                // 评分（1-5）
      comment?: string;              // 评价内容
      photoURLs?: string;            // 图片URL，多个用逗号分隔
      createdAt: string;             // 评价时间 ISO格式
      order?: {
        sn: string;                  // 订单编号
      };
      customer?: {
        id: number;
        name: string;                // 客户姓名
        phone: string;               // 客户电话
        memberStatus: string;        // 会员状态
        points: number;              // 积分
        status: number;              // 状态
      };
      employee?: {
        id: number;
        name: string;                // 员工姓名
        phone: string;               // 员工电话
        level?: number;              // 员工等级
        rating?: number;             // 员工评分
        walletBalance: number;       // 钱包余额
        status: number;              // 状态
      };
    }>;
  };
}
```

### 2. 评价统计概览接口
**接口地址：** `GET /reviews/statistics`

**请求参数：**
```typescript
{
  startDate?: string;      // 开始日期 YYYY-MM-DD
  endDate?: string;        // 结束日期 YYYY-MM-DD
}
```

**返回数据结构：**
```typescript
{
  errCode: number;
  msg?: string;
  data?: {
    totalReviews: number;      // 总评价数
    todayReviews: number;      // 今日评价数
    averageRating: number;     // 平均评分（保留1位小数）
    positiveRate: number;      // 好评率（4-5星占比，保留1位小数）
    thisWeekReviews: number;   // 本周评价数
    thisMonthReviews: number;  // 本月评价数
  };
}
```

### 3. 评价趋势数据接口
**接口地址：** `GET /reviews/trend`

**请求参数：**
```typescript
{
  startDate: string;       // 开始日期 YYYY-MM-DD（必填）
  endDate: string;         // 结束日期 YYYY-MM-DD（必填）
}
```

**返回数据结构：**
```typescript
{
  errCode: number;
  msg?: string;
  data?: Array<{
    date: string;            // 日期 YYYY-MM-DD
    reviews: number;         // 当日评价数量
    averageRating: number;   // 当日平均评分（保留1位小数）
  }>;
}
```

### 4. 评分分布统计接口
**接口地址：** `GET /reviews/rating-distribution`

**请求参数：**
```typescript
{
  startDate?: string;      // 开始日期 YYYY-MM-DD
  endDate?: string;        // 结束日期 YYYY-MM-DD
}
```

**返回数据结构：**
```typescript
{
  errCode: number;
  msg?: string;
  data?: Array<{
    rating: number;          // 评分（1-5）
    count: number;           // 该评分的数量
    percentage: number;      // 占比（保留1位小数）
  }>;
}
```

### 5. 员工评分排行接口
**接口地址：** `GET /reviews/employee-ranking`

**请求参数：**
```typescript
{
  limit?: number;          // 返回条数，默认10
  startDate?: string;      // 开始日期 YYYY-MM-DD
  endDate?: string;        // 结束日期 YYYY-MM-DD
}
```

**返回数据结构：**
```typescript
{
  errCode: number;
  msg?: string;
  data?: Array<{
    employeeId: number;      // 员工ID
    employeeName: string;    // 员工姓名
    averageRating: number;   // 平均评分（保留1位小数）
    reviewCount: number;     // 评价总数
    level: number;           // 员工等级
  }>;
}
```

## 数据库设计建议

基于现有的评价表结构，建议确保以下字段存在：

### reviews 表
```sql
CREATE TABLE reviews (
  id INT PRIMARY KEY AUTO_INCREMENT,
  orderId INT NOT NULL,                    -- 关联订单ID
  rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5), -- 评分1-5
  comment TEXT,                            -- 评价内容
  photoURLs TEXT,                          -- 图片URLs，逗号分隔
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_order_id (orderId),
  INDEX idx_rating (rating),
  INDEX idx_created_at (createdAt)
);
```

## 注意事项

1. **日期格式**：所有日期参数和返回值统一使用 ISO 8601 格式（YYYY-MM-DDTHH:mm:ss.sssZ）
2. **分页**：列表接口需要支持分页，返回 total 总数和 list 数据列表
3. **错误处理**：所有接口都需要返回统一的错误码格式
4. **性能优化**：建议对常用查询字段建立索引，特别是 orderId、rating、createdAt
5. **数据关联**：评价数据需要关联订单、客户、员工信息，建议使用 JOIN 查询或分步查询
6. **好评率计算**：好评率 = (4星+5星评价数) / 总评价数 * 100
7. **图片处理**：photoURLs 字段存储多个图片URL时，使用逗号分隔

## 前端已实现功能

1. ✅ 评价数据概览统计卡片
2. ✅ 评价趋势图表（简单SVG实现）
3. ✅ 评分分布统计图
4. ✅ 评价列表表格（支持搜索、筛选、分页）
5. ✅ 评价详情弹窗（支持图片预览）
6. ✅ 日期范围筛选
7. ✅ 数据刷新功能

## 待后台实现

1. 🔄 实际的API接口调用（目前使用模拟数据）
2. 🔄 数据库查询优化
3. 🔄 统计数据计算逻辑
4. 🔄 图片存储和访问处理
