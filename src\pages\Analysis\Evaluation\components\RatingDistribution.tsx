import { ProCard } from '@ant-design/pro-components';
import { Empty, Progress } from 'antd';
import React, { useEffect, useState } from 'react';

interface RatingDistributionData {
  rating: number;
  count: number;
  percentage: number;
}

const RatingDistribution: React.FC = () => {
  const [distributionData, setDistributionData] = useState<RatingDistributionData[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取评分分布数据
  const fetchDistributionData = async () => {
    setLoading(true);
    try {
      // TODO: 调用实际的API
      // const { errCode, msg, data } = await reviews.ratingDistribution();

      // 模拟数据
      const mockData: RatingDistributionData[] = [
        { rating: 5, count: 580, percentage: 58.0 },
        { rating: 4, count: 320, percentage: 32.0 },
        { rating: 3, count: 70, percentage: 7.0 },
        { rating: 2, count: 20, percentage: 2.0 },
        { rating: 1, count: 10, percentage: 1.0 },
      ];
      
      setDistributionData(mockData);
    } catch (error) {
      console.error('获取评分分布数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDistributionData();
  }, []);

  // 渲染分布图
  const renderDistribution = () => {
    if (distributionData.length === 0) {
      return <Empty description="暂无数据" />;
    }

    const colors = ['#ff4d4f', '#ff7a45', '#ffa940', '#52c41a', '#1890ff'];

    return (
      <div style={{ padding: '20px 0' }}>
        {distributionData.map((item, index) => (
          <div key={item.rating} style={{ marginBottom: 16 }}>
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              marginBottom: 4 
            }}>
              <span style={{ 
                width: 60, 
                fontSize: '14px', 
                fontWeight: 500 
              }}>
                {item.rating} 星
              </span>
              <div style={{ flex: 1, marginLeft: 12, marginRight: 12 }}>
                <Progress
                  percent={item.percentage}
                  strokeColor={colors[4 - index]}
                  showInfo={false}
                  size="small"
                />
              </div>
              <span style={{ 
                width: 80, 
                textAlign: 'right', 
                fontSize: '12px', 
                color: '#666' 
              }}>
                {item.count} 条 ({item.percentage}%)
              </span>
            </div>
          </div>
        ))}
        
        {/* 总计信息 */}
        <div style={{ 
          marginTop: 20, 
          padding: '12px 0', 
          borderTop: '1px solid #f0f0f0',
          textAlign: 'center',
          fontSize: '12px',
          color: '#999'
        }}>
          总评价数: {distributionData.reduce((sum, item) => sum + item.count, 0)} 条
        </div>
      </div>
    );
  };

  return (
    <ProCard
      title="评分分布统计"
      loading={loading}
      style={{ height: '300px' }}
    >
      {renderDistribution()}
    </ProCard>
  );
};

export default RatingDistribution;
