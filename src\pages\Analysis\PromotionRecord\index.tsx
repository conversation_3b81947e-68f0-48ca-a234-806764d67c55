import { promotionRecord } from '@/services';
import { getCustomer } from '@/services/customers';
import {
  EyeOutlined,
  ReloadOutlined,
  ShareAltOutlined,
  TeamOutlined,
  TrophyOutlined,
  UserAddOutlined,
} from '@ant-design/icons';
import {
  ActionType,
  PageContainer,
  ProCard,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import {
  Button,
  Card,
  Col,
  message,
  Row,
  Space,
  Statistic,
  Tooltip,
} from 'antd';
import moment from 'moment';
import React, { useEffect, useRef, useState } from 'react';
import UserDetailDrawer from './components/UserDetailDrawer';

interface StatisticsData {
  totalRecords: number;
  todayRecords: number;
  activeSharers: number;
  thisWeekRecords: number;
  thisMonthRecords: number;
  avgDailyRecords: number;
}

const PromotionRecord: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState<StatisticsData>({
    totalRecords: 0,
    todayRecords: 0,
    activeSharers: 0,
    thisWeekRecords: 0,
    thisMonthRecords: 0,
    avgDailyRecords: 0,
  });

  // 用户详情相关状态
  const [userDetailVisible, setUserDetailVisible] = useState(false);
  const [currentUser, setCurrentUser] = useState<API.Customer | null>(null);
  const [userDetailTitle, setUserDetailTitle] = useState('');

  // 获取统计数据
  const fetchStatistics = async () => {
    setLoading(true);
    try {
      const { errCode, msg, data } = await promotionRecord.statistics();

      if (errCode) {
        message.error(msg || '获取统计数据失败');
        return;
      }

      if (data) {
        setStatistics({
          totalRecords: data.totalRecords,
          todayRecords: data.todayRecords,
          activeSharers: data.activeSharers,
          thisWeekRecords: data.thisWeekRecords,
          thisMonthRecords: data.thisMonthRecords,
          avgDailyRecords: data.avgDailyRecords,
        });
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
      message.error('获取统计数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatistics();
  }, []);

  // 查看用户详情
  const handleViewUserDetail = async (userId: number, title: string) => {
    setUserDetailTitle(title);
    setUserDetailVisible(true);

    try {
      const { errCode, msg, data } = await getCustomer(userId);

      if (errCode) {
        message.error(msg || '获取用户详情失败');
        setCurrentUser(null);
        return;
      }

      setCurrentUser(data || null);
    } catch (error) {
      console.error('获取用户详情失败:', error);
      message.error('获取用户详情失败');
      setCurrentUser(null);
    }
  };

  // 关闭用户详情
  const handleCloseUserDetail = () => {
    setUserDetailVisible(false);
    setCurrentUser(null);
    setUserDetailTitle('');
  };

  // 刷新数据
  const handleRefresh = () => {
    actionRef.current?.reload();
    fetchStatistics();
  };

  const columns: ProColumns<API.PromotionRecord, 'text'>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      hideInTable: true,
      hideInSearch: true,
    },
    {
      title: '分享人',
      dataIndex: ['sharer', 'nickname'],
      key: 'sharerOpenName',
      width: 120,
      render: (_, record) => (
        <Space>
          <span>{record.sharer?.nickname || '未知用户'}</span>
          <Tooltip title="查看分享人详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => {
                if (record.sharerUserId) {
                  handleViewUserDetail(record.sharerUserId, '分享人详情');
                } else {
                  message.warning('分享人信息不完整');
                }
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
    {
      title: '分享人ID',
      dataIndex: 'sharerUserId',
      key: 'sharerUserId',
      width: 100,
      hideInTable: true,
      hideInSearch: true,
    },
    {
      title: '分享时间',
      dataIndex: 'shareTime',
      key: 'shareTime',
      valueType: 'date',
      width: 160,
      sorter: true,
    },
    {
      title: '注册人',
      dataIndex: ['registrant', 'nickname'],
      key: 'registrantOpenName',
      width: 120,
      render: (_, record) => (
        <Space>
          <span>{record.registrant?.nickname || '未知用户'}</span>
          <Tooltip title="查看注册人详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => {
                if (record.registrantUserId) {
                  handleViewUserDetail(record.registrantUserId, '注册人详情');
                } else {
                  message.warning('注册人信息不完整');
                }
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
    {
      title: '注册时间',
      dataIndex: 'registerTime',
      key: 'registerTime',
      valueType: 'date',
      width: 160,
      sorter: true,
      render: (_, record) => {
        return record.registerTime
          ? moment(record.registerTime).format('YYYY-MM-DD HH:mm:ss')
          : '-';
      },
    },
    {
      title: '推广码',
      dataIndex: 'promotionCode',
      key: 'promotionCode',
      width: 120,
      copyable: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      valueType: 'select',
      filters: true,
      valueEnum: {
        有效: { text: '有效', status: 'Success' },
        已使用: { text: '已使用', status: 'Processing' },
        过期: { text: '过期', status: 'Default' },
      },
      hideInSearch: true,
    },
  ];

  return (
    <PageContainer
      header={{
        title: '推广数据分析',
        subTitle: '查看和分析用户推广数据，了解推广效果和转化情况',
      }}
      breadcrumb={{ items: [] }}
    >
      {/* 统计卡片区域 */}
      <ProCard
        title="数据概览"
        extra={
          <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
            刷新
          </Button>
        }
        style={{ marginBottom: 16 }}
        loading={loading}
      >
        <Row gutter={[16, 16]}>
          <Col xs={12} sm={8} md={6} lg={4} xl={4}>
            <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
              <Statistic
                title="总推广记录"
                value={statistics.totalRecords}
                prefix={<ShareAltOutlined style={{ color: '#1890ff' }} />}
                valueStyle={{ color: '#1890ff', fontSize: '18px' }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4} xl={4}>
            <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
              <Statistic
                title="今日推广"
                value={statistics.todayRecords}
                prefix={<UserAddOutlined style={{ color: '#52c41a' }} />}
                valueStyle={{ color: '#52c41a', fontSize: '18px' }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4} xl={4}>
            <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
              <Statistic
                title="本周推广"
                value={statistics.thisWeekRecords}
                prefix={<TrophyOutlined style={{ color: '#faad14' }} />}
                valueStyle={{ color: '#faad14', fontSize: '18px' }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4} xl={4}>
            <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
              <Statistic
                title="本月推广"
                value={statistics.thisMonthRecords}
                prefix={<ShareAltOutlined style={{ color: '#722ed1' }} />}
                valueStyle={{ color: '#722ed1', fontSize: '18px' }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4} xl={4}>
            <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
              <Statistic
                title="日均推广"
                value={statistics.avgDailyRecords}
                prefix={<UserAddOutlined style={{ color: '#eb2f96' }} />}
                valueStyle={{ color: '#eb2f96', fontSize: '18px' }}
                precision={1}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4} xl={4}>
            <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
              <Statistic
                title="活跃分享者"
                value={statistics.activeSharers}
                prefix={<TeamOutlined style={{ color: '#13c2c2' }} />}
                valueStyle={{ color: '#13c2c2', fontSize: '18px' }}
              />
            </Card>
          </Col>
        </Row>
      </ProCard>

      {/* 数据表格区域 */}
      <ProCard>
        <ProTable<API.PromotionRecord>
          actionRef={actionRef}
          rowKey="id"
          headerTitle="推广记录详情"
          columns={columns}
          search={{
            labelWidth: 'auto',
          }}
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
          }}
          scroll={{ x: 1200 }}
          request={async (params, sort, filter) => {
            const { errCode, msg, data } = await promotionRecord.index({
              ...params,
              ...sort,
              filter,
            });
            if (errCode) {
              message.error(msg || '列表查询失败');
              return {
                data: [],
                total: 0,
                success: false,
              };
            }
            return {
              data: data?.list || [],
              total: data?.total || 0,
              success: true,
            };
          }}
          toolBarRender={() => [
            <Button
              key="refresh"
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
            >
              刷新
            </Button>,
          ]}
          options={{
            reload: true,
            density: true,
            fullScreen: true,
            setting: true,
          }}
        />
      </ProCard>

      {/* 用户详情抽屉 */}
      <UserDetailDrawer
        open={userDetailVisible}
        onClose={handleCloseUserDetail}
        user={currentUser}
        title={userDetailTitle}
      />
    </PageContainer>
  );
};

export default PromotionRecord;
